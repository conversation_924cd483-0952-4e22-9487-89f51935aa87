import { sql } from "drizzle-orm";
import { pgTable, text, varchar, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const applications = pgTable("applications", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  fullName: text("full_name").notNull(),
  phone: text("phone").notNull(),
  city: text("city").notNull(),
  email: text("email"),
  truckType: text("truck_type").notNull(),
  truckYear: text("truck_year").notNull(),
  registration: text("registration").notNull(),
  idCardPath: text("id_card_path"),
  licensePath: text("license_path"),
  truckPhotoPath: text("truck_photo_path"),
  status: text("status").notNull().default("pending"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertApplicationSchema = createInsertSchema(applications).omit({
  id: true,
  createdAt: true,
}).extend({
  fullName: z.string().min(2, "Le nom complet est requis"),
  phone: z.string().min(10, "Numéro de téléphone valide requis"),
  city: z.string().min(1, "La ville est requise"),
  email: z.string().email("Email valide requis").optional().or(z.literal("")),
  truckType: z.string().min(1, "Le type de camion est requis"),
  truckYear: z.string().min(4, "L'année du camion est requise"),
  registration: z.string().min(1, "Le numéro d'immatriculation est requis"),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type Application = typeof applications.$inferSelect;
export type InsertApplication = z.infer<typeof insertApplicationSchema>;
