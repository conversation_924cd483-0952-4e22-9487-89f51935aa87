import type { Express } from "express";
import express from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertApplicationSchema } from "@shared/schema";
import multer from "multer";
import path from "path";
import fs from "fs";

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage_multer = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage_multer,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only images and PDF files are allowed'));
    }
  }
});

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Submit driver application
  app.post("/api/applications", upload.fields([
    { name: 'idCard', maxCount: 1 },
    { name: 'license', maxCount: 1 },
    { name: 'truckPhoto', maxCount: 1 }
  ]), async (req, res) => {
    try {
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };
      
      const applicationData = {
        fullName: req.body.fullName,
        phone: req.body.phone,
        city: req.body.city,
        email: req.body.email || "",
        truckType: req.body.truckType,
        truckYear: req.body.truckYear,
        registration: req.body.registration,
      };

      // Validate the application data
      const validatedData = insertApplicationSchema.parse(applicationData);
      
      // Create the application
      const application = await storage.createApplication(validatedData);
      
      // Update with file paths if files were uploaded
      const updates: any = {};
      if (files.idCard?.[0]) {
        updates.idCardPath = files.idCard[0].filename;
      }
      if (files.license?.[0]) {
        updates.licensePath = files.license[0].filename;
      }
      if (files.truckPhoto?.[0]) {
        updates.truckPhotoPath = files.truckPhoto[0].filename;
      }

      let updatedApplication = application;
      if (Object.keys(updates).length > 0) {
        updatedApplication = await storage.updateApplication(application.id, updates) || application;
      }

      res.json(updatedApplication);
    } catch (error: any) {
      console.error("Application submission error:", error);
      res.status(400).json({ 
        message: error.message || "Erreur lors de la soumission de la candidature" 
      });
    }
  });

  // Get all applications (for admin)
  app.get("/api/applications", async (req, res) => {
    try {
      const applications = await storage.getApplications();
      res.json(applications);
    } catch (error: any) {
      console.error("Error fetching applications:", error);
      res.status(500).json({ message: "Erreur lors de la récupération des candidatures" });
    }
  });

  // Serve uploaded files
  app.use("/uploads", express.static(uploadsDir));

  const httpServer = createServer(app);
  return httpServer;
}
