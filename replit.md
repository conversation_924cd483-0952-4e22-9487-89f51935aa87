# replit.md

## Overview

This is a full-stack web application for a logistics company (TransLogistics) that facilitates driver recruitment. The application allows truck drivers to submit applications to join the company's network. It features a modern, responsive frontend built with React and a RESTful API backend using Express.js.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

The application follows a monorepo structure with clear separation between client and server code:

- **Frontend**: React with TypeScript, using Vite as the build tool
- **Backend**: Express.js with TypeScript for API endpoints
- **Database**: PostgreSQL with Drizzle ORM for data persistence
- **Styling**: TailwindCSS with shadcn/ui component library
- **State Management**: TanStack Query for server state management
- **Internationalization**: i18next for French/Arabic language support

## Key Components

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite with hot module replacement
- **Routing**: Wouter for client-side routing
- **Styling**: TailwindCSS with custom design tokens
- **UI Components**: shadcn/ui (Radix UI primitives)
- **Forms**: React Hook Form with Zod validation
- **Internationalization**: i18next with RTL support for Arabic

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **Database ORM**: Drizzle ORM with PostgreSQL
- **File Upload**: Multer for handling multipart form data
- **Session Management**: Connect-pg-simple for PostgreSQL session store
- **API Design**: RESTful endpoints with JSON responses

### Database Schema
- **Users Table**: Basic user authentication (id, username, password)
- **Applications Table**: Driver application data including:
  - Personal info (name, phone, city, email)
  - Truck details (type, year, registration)
  - Document file paths (ID card, license, truck photo)
  - Application status and timestamps

### File Storage
- Local file system storage in `uploads/` directory
- Multer configuration for image and PDF uploads (5MB limit)
- Unique filename generation with timestamps

## Data Flow

1. **Application Submission**: Users fill out the multi-step form on `/apply` page
2. **File Upload**: Documents are uploaded via multipart form data
3. **Validation**: Form data validated using Zod schemas on both client and server
4. **Storage**: Application data stored in PostgreSQL, files saved to local filesystem
5. **Response**: Success/error feedback provided to user via toast notifications

## External Dependencies

### Frontend Dependencies
- **UI Framework**: React, React-DOM
- **Routing**: wouter
- **State Management**: @tanstack/react-query
- **Forms**: react-hook-form, @hookform/resolvers
- **Validation**: zod, drizzle-zod
- **Styling**: tailwindcss, class-variance-authority, clsx
- **UI Components**: Various @radix-ui packages
- **Icons**: lucide-react
- **Internationalization**: react-i18next

### Backend Dependencies
- **Server**: express
- **Database**: drizzle-orm, @neondatabase/serverless
- **File Upload**: multer, @types/multer
- **Session**: express-session, connect-pg-simple
- **Utilities**: Various utility packages

### Development Dependencies
- **Build**: typescript, tsx, esbuild
- **Linting/Formatting**: Standard TypeScript configuration
- **Database Tools**: drizzle-kit for migrations

## Deployment Strategy

The application is configured for multiple deployment scenarios:

### Development
- **Frontend**: Vite dev server with HMR
- **Backend**: tsx for TypeScript execution with file watching
- **Database**: Local PostgreSQL or cloud provider (Neon)

### Production
- **Build Process**: 
  - Frontend built with Vite to `dist/public`
  - Backend bundled with esbuild to `dist/index.js`
- **Serving**: Express serves both API routes and static frontend files
- **Database**: PostgreSQL connection via DATABASE_URL environment variable

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `NODE_ENV`: Environment setting (development/production)

The application includes Replit-specific configurations for seamless deployment on the Replit platform, including cartographer plugin for enhanced development experience.