@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Almarai:wght@300;400;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(0, 0%, 15%);
  --muted: hsl(210, 40%, 98%);
  --muted-foreground: hsl(215, 13%, 65%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 15%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 15%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --primary: hsl(210, 100%, 32%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(210, 40%, 98%);
  --secondary-foreground: hsl(222, 84%, 5%);
  --accent: hsl(210, 40%, 98%);
  --accent-foreground: hsl(222, 84%, 5%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(210, 100%, 32%);
  --radius: 0.75rem;
  
  /* Professional color palette */
  --success: hsl(142, 76%, 36%);
  --warning: hsl(38, 92%, 50%);
  --neutral: hsl(210, 20%, 98%);
  --dark-grey: hsl(215, 25%, 27%);
  --light-grey: hsl(210, 16%, 93%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  html[dir="rtl"] {
    font-family: 'Almarai', 'Inter', sans-serif;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .hero-gradient {
    background: linear-gradient(135deg, 
      hsl(210, 100%, 32%) 0%, 
      hsl(210, 100%, 40%) 50%, 
      hsl(210, 100%, 35%) 100%);
  }
  
  .hero-overlay {
    background: linear-gradient(135deg, 
      rgba(0, 87, 163, 0.85) 0%, 
      rgba(0, 87, 163, 0.75) 50%, 
      rgba(0, 87, 163, 0.85) 100%);
  }
  
  .section-divider {
    background: linear-gradient(90deg, transparent, hsl(210, 100%, 32%), transparent);
    height: 3px;
    border-radius: 2px;
  }
  
  /* Animation classes with fallbacks */
  .fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
  }
  
  .slide-up {
    animation: slideUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }
  
  .slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
    opacity: 0;
    transform: translateX(-30px);
  }
  
  .slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
    opacity: 0;
    transform: translateX(30px);
  }
  
  /* Fallback for users with reduced motion preference */
  @media (prefers-reduced-motion: reduce) {
    .fade-in,
    .slide-up,
    .slide-in-left,
    .slide-in-right {
      animation: none;
      opacity: 1;
      transform: none;
    }
  }
  
  /* Ensure elements are visible after animation completes */
  .fade-in.animate-complete,
  .slide-up.animate-complete,
  .slide-in-left.animate-complete,
  .slide-in-right.animate-complete {
    opacity: 1;
    transform: none;
  }
  
  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideInLeft {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideInRight {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Staggered animation delays */
  .delay-100 { animation-delay: 0.1s; }
  .delay-200 { animation-delay: 0.2s; }
  .delay-300 { animation-delay: 0.3s; }
  .delay-400 { animation-delay: 0.4s; }
  .delay-500 { animation-delay: 0.5s; }
  
  /* Professional button styles */
  .btn-primary {
    background: linear-gradient(135deg, hsl(210, 100%, 32%) 0%, hsl(210, 100%, 40%) 100%);
    box-shadow: 0 4px 14px 0 hsla(210, 100%, 32%, 0.3);
    transition: all 0.3s ease;
  }
  
  .btn-primary:hover {
    background: linear-gradient(135deg, hsl(210, 100%, 40%) 0%, hsl(210, 100%, 45%) 100%);
    box-shadow: 0 6px 20px 0 hsla(210, 100%, 32%, 0.4);
    transform: translateY(-2px);
  }
  
  .btn-warning {
    background: linear-gradient(135deg, hsl(38, 92%, 50%) 0%, hsl(38, 92%, 55%) 100%);
    box-shadow: 0 4px 14px 0 hsla(38, 92%, 50%, 0.3);
    transition: all 0.3s ease;
  }
  
  .btn-warning:hover {
    background: linear-gradient(135deg, hsl(38, 92%, 55%) 0%, hsl(38, 92%, 60%) 100%);
    box-shadow: 0 6px 20px 0 hsla(38, 92%, 50%, 0.4);
    transform: translateY(-2px);
  }
  
  /* Custom utility classes */
  .text-primary {
    color: hsl(210, 100%, 32%);
  }
  
  .bg-primary {
    background-color: hsl(210, 100%, 32%);
  }
  
  .text-success {
    color: hsl(142, 76%, 36%);
  }
  
  .bg-success {
    background-color: hsl(142, 76%, 36%);
  }
  
  .text-warning {
    color: hsl(38, 92%, 50%);
  }
  
  .bg-warning {
    background-color: hsl(38, 92%, 50%);
  }
  
  .text-neutral {
    color: hsl(210, 20%, 98%);
  }
  
  .bg-neutral {
    background-color: hsl(210, 20%, 98%);
  }
  
  .text-dark-grey {
    color: hsl(215, 25%, 27%);
  }
  
  .bg-dark-grey {
    background-color: hsl(215, 25%, 27%);
  }
  
  .text-light-grey {
    color: hsl(210, 16%, 93%);
  }
  
  .bg-light-grey {
    background-color: hsl(210, 16%, 93%);
  }
}

/* RTL Support for Arabic */
html[dir="rtl"] {
  .rtl\:text-right {
    text-align: right;
  }
  
  .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }
}
