import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { 
  Truck, 
  MapPin, 
  Phone, 
  Star, 
  Shield, 
  Clock, 
  Users,
  ArrowRight,
  CheckCircle
} from "lucide-react";

export function ProfessionalBanner() {
  const { t } = useTranslation();

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="bg-gradient-to-r from-primary via-blue-600 to-primary text-white py-4 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-15">
        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
          <div className="flex space-x-12">
            <Truck size={60} className="opacity-20 transform rotate-12" />
            <Truck size={80} className="opacity-20" />
            <Truck size={60} className="opacity-20 transform -rotate-12" />
          </div>
        </div>
        <div className="absolute top-2 left-10 transform rotate-12">
          <Truck size={24} />
        </div>
        <div className="absolute top-8 right-20 transform -rotate-12">
          <Truck size={20} />
        </div>
        <div className="absolute bottom-2 left-1/3 transform rotate-45">
          <Truck size={16} />
        </div>
        <div className="absolute bottom-4 right-1/4 transform -rotate-45">
          <Truck size={18} />
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
          {/* Left Section - Main Message */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-12 h-12 bg-warning rounded-full flex items-center justify-center animate-pulse">
                <Truck className="text-dark-grey" size={24} />
              </div>
              <div>
                <h2 className="text-lg font-bold">
                  🚛 {t('banner.title')}
                </h2>
                <p className="text-sm opacity-90">
                  {t('banner.subtitle')}
                </p>
              </div>
            </div>
          </div>

          {/* Center Section - Key Benefits */}
          <div className="hidden md:flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-1">
              <CheckCircle size={16} className="text-success" />
              <span>{t('banner.benefits.payments')}</span>
            </div>
            <div className="flex items-center space-x-1">
              <CheckCircle size={16} className="text-success" />
              <span>{t('banner.benefits.drivers')}</span>
            </div>
            <div className="flex items-center space-x-1">
              <CheckCircle size={16} className="text-success" />
              <span>{t('banner.benefits.support')}</span>
            </div>
          </div>

          {/* Right Section - CTA */}
          <div className="flex items-center space-x-3">
            <Button
              onClick={() => scrollToSection('apply')}
              className="bg-warning text-dark-grey px-6 py-2 font-bold hover:bg-yellow-400 transition-all duration-300 transform hover:scale-105"
            >
              <Users className="mr-2" size={16} />
              {t('banner.apply')}
            </Button>
            <a
              href="tel:+212500000000"
              className="flex items-center space-x-1 text-sm hover:text-warning transition-colors"
            >
              <Phone size={16} />
              <span className="hidden sm:inline">{t('banner.call')}</span>
            </a>
          </div>
        </div>
      </div>

      {/* Animated Border */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-warning via-yellow-400 to-warning animate-pulse"></div>
    </div>
  );
}
