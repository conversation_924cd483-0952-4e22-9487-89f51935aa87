import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { 
  Truck, 
  MapPin, 
  Phone, 
  Mail,
  Instagram,
  Facebook,
  Linkedin,
  Youtube,
  ExternalLink
} from "lucide-react";

export function Footer() {
  const { t } = useTranslation();

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <footer className="bg-dark-grey text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
          
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Truck className="text-white" size={24} />
              </div>
              <span className="text-2xl font-bold text-white">{t('footer.company')}</span>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {t('footer.description')}
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-300">
                <Phone size={16} className="text-primary" />
                <span>+*********** 000</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <Mail size={16} className="text-primary" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <MapPin size={16} className="text-primary" />
                <span>Casablanca, Maroc</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6">{t('footer.quickLinks')}</h3>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => scrollToSection('home')}
                  className="text-gray-300 hover:text-warning transition-colors"
                >
                  {t('nav.home')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('about')}
                  className="text-gray-300 hover:text-warning transition-colors"
                >
                  {t('footer.about')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('how-it-works')}
                  className="text-gray-300 hover:text-warning transition-colors"
                >
                  {t('nav.howItWorks')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="text-gray-300 hover:text-warning transition-colors"
                >
                  {t('footer.contact')}
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('apply')}
                  className="text-gray-300 hover:text-warning transition-colors"
                >
                  {t('footer.apply')}
                </button>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-xl font-bold mb-6">{t('footer.services')}</h3>
            <ul className="space-y-3 text-gray-300">
              <li>🚛 Transport de Marchandises</li>
              <li>📦 Logistique</li>
              <li>🌍 Transport Inter-ville</li>
              <li>⚡ Livraison Express</li>
              <li>🔒 Transport Sécurisé</li>
            </ul>
          </div>

          {/* Social Media */}
          <div>
            <h3 className="text-xl font-bold mb-6">{t('footer.followUs')}</h3>
            <p className="text-gray-300 mb-6">{t('footer.socialMedia')}</p>
            
            <div className="grid grid-cols-2 gap-4">
              {/* Instagram */}
              <a
                href="https://instagram.com/translogistics_ma"
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl p-4 text-center hover:scale-105 transition-all duration-300 shadow-lg">
                  <div className="text-white text-2xl mb-2">📷</div>
                  <span className="text-white text-sm font-semibold">{t('footer.instagram')}</span>
                  <ExternalLink size={12} className="text-white/70 ml-1 inline" />
                </div>
              </a>

              {/* Facebook */}
              <a
                href="https://facebook.com/translogistics.ma"
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl p-4 text-center hover:scale-105 transition-all duration-300 shadow-lg">
                  <div className="text-white text-2xl mb-2">📘</div>
                  <span className="text-white text-sm font-semibold">{t('footer.facebook')}</span>
                  <ExternalLink size={12} className="text-white/70 ml-1 inline" />
                </div>
              </a>

              {/* LinkedIn */}
              <a
                href="https://linkedin.com/company/translogistics-ma"
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="bg-gradient-to-br from-blue-700 to-blue-800 rounded-xl p-4 text-center hover:scale-105 transition-all duration-300 shadow-lg">
                  <div className="text-white text-2xl mb-2">💼</div>
                  <span className="text-white text-sm font-semibold">{t('footer.linkedin')}</span>
                  <ExternalLink size={12} className="text-white/70 ml-1 inline" />
                </div>
              </a>

              {/* YouTube */}
              <a
                href="https://youtube.com/@translogistics-ma"
                target="_blank"
                rel="noopener noreferrer"
                className="group"
              >
                <div className="bg-gradient-to-br from-red-600 to-red-700 rounded-xl p-4 text-center hover:scale-105 transition-all duration-300 shadow-lg">
                  <div className="text-white text-2xl mb-2">📺</div>
                  <span className="text-white text-sm font-semibold">{t('footer.youtube')}</span>
                  <ExternalLink size={12} className="text-white/70 ml-1 inline" />
                </div>
              </a>
            </div>

            {/* WhatsApp Quick Contact */}
            <div className="mt-6">
              <a
                href="https://wa.me/212600000000?text=Bonjour, je suis chauffeur avec un camion, je veux rejoindre votre réseau."
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-4 text-center hover:scale-105 transition-all duration-300 shadow-lg">
                  <div className="text-white text-2xl mb-2">💬</div>
                  <span className="text-white font-semibold">WhatsApp</span>
                  <div className="text-white/80 text-xs mt-1">Contact Direct</div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-600">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © 2024 TransLogistics. {t('footer.rights')}
            </div>
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-warning transition-colors">
                {t('footer.privacy')}
              </a>
              <a href="#" className="text-gray-400 hover:text-warning transition-colors">
                {t('footer.terms')}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
