import { useState } from "react";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { LanguageSwitcher } from "./LanguageSwitcher";
import { ProfessionalBanner } from "./ProfessionalBanner";
import { useTranslation } from "react-i18next";
import { Menu, X, Truck, Phone, MapPin } from "lucide-react";

export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [location] = useLocation();
  const { t } = useTranslation();

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    setIsMobileMenuOpen(false);
  };

  const navigation = [
    { name: t('nav.home'), sectionId: "home" },
    { name: t('nav.about'), sectionId: "about" },
    { name: t('nav.howItWorks'), sectionId: "how-it-works" },
    { name: t('nav.contact'), sectionId: "contact" },
  ];

  return (
    <div className="fixed w-full top-0 z-50">
      <ProfessionalBanner />
      <nav className="bg-white shadow-lg">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <Truck className="text-white" size={20} />
            </div>
            <span className="text-xl font-bold text-primary">TransLogistics</span>
          </Link>
          
          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <button
                key={item.sectionId}
                onClick={() => scrollToSection(item.sectionId)}
                className="text-dark-grey hover:text-primary transition-colors font-medium"
              >
                {item.name}
              </button>
            ))}
            <LanguageSwitcher />
          </div>
          
          {/* CTA Button */}
          <Button 
            onClick={() => scrollToSection('apply')}
            className="hidden md:block bg-primary text-white px-6 py-2 font-semibold hover:bg-blue-700"
          >
            {t('nav.applyNow')}
          </Button>
          
          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>
        
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white border-t">
            <div className="px-4 py-3 space-y-3">
              {navigation.map((item) => (
                <button
                  key={item.sectionId}
                  onClick={() => scrollToSection(item.sectionId)}
                  className="block text-dark-grey hover:text-primary"
                >
                  {item.name}
                </button>
              ))}
              <div className="pt-2">
                <LanguageSwitcher />
              </div>
              <Button 
                onClick={() => scrollToSection('apply')}
                className="w-full bg-primary text-white py-3 font-semibold mt-3"
              >
                {t('nav.applyNow')}
              </Button>
            </div>
          </div>
        )}
        </div>
      </nav>
    </div>
  );
}
