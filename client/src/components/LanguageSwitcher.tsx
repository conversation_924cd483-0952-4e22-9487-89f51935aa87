import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

export function LanguageSwitcher() {
  const { i18n } = useTranslation();

  const toggleLanguage = () => {
    const newLang = i18n.language === 'fr' ? 'ar' : 'fr';
    i18n.changeLanguage(newLang);
    document.documentElement.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant={i18n.language === 'fr' ? "default" : "outline"}
        size="sm"
        onClick={() => {
          i18n.changeLanguage('fr');
          document.documentElement.dir = 'ltr';
        }}
        className="text-xs px-3 py-1"
      >
        FR
      </Button>
      <Button
        variant={i18n.language === 'ar' ? "default" : "outline"}
        size="sm"
        onClick={() => {
          i18n.changeLanguage('ar');
          document.documentElement.dir = 'rtl';
        }}
        className="text-xs px-3 py-1"
      >
        AR
      </Button>
    </div>
  );
}
