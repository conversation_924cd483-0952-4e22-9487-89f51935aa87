import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { insertApplicationSchema } from "@shared/schema";
import {
  DollarSign, Route, Headphones, ChevronDown, UserPlus, PlayCircle,
  User, Truck, Upload, Send, Shield, ArrowRight, Rocket,
  MessageCircle, Phone, Mail, CheckCircle, MapPin, Calendar
} from "lucide-react";

export default function Home() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [files, setFiles] = useState<{
    idCard?: File;
    license?: File;
    truckPhoto?: File;
  }>({});

  const form = useForm({
    resolver: zodResolver(insertApplicationSchema),
    defaultValues: {
      fullName: "",
      phone: "",
      city: "",
      email: "",
      truckType: "",
      truckYear: "",
      registration: "",
    },
  });

  const submitApplication = useMutation({
    mutationFn: async (data: any) => {
      const formData = new FormData();
      
      // Add form fields
      Object.keys(data).forEach(key => {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      });
      
      // Add files
      if (files.idCard) formData.append('idCard', files.idCard);
      if (files.license) formData.append('license', files.license);
      if (files.truckPhoto) formData.append('truckPhoto', files.truckPhoto);
      
      const response = await fetch('/api/applications', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erreur lors de la soumission');
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Candidature envoyée!",
        description: "Merci pour votre candidature. Nous vous contacterons bientôt.",
      });
      form.reset();
      setFiles({});
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.message || "Erreur lors de la soumission de la candidature",
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (field: 'idCard' | 'license' | 'truckPhoto', file: File | null) => {
    if (file) {
      setFiles(prev => ({ ...prev, [field]: file }));
    }
  };

  const cities = [
    'casablanca', 'rabat', 'fes', 'marrakech', 'agadir', 'tanger', 
    'meknes', 'oujda', 'kenitra', 'tetouan', 'other'
  ];

  const truckTypes = [
    'porteur', 'semiRemorque', 'fourgon', 'frigorifique', 'citerne', 'benne'
  ];

  const phoneNumber = "+212500000000";
  const whatsappNumber = "+212600000000";
  const email = "<EMAIL>";

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center pt-24 overflow-hidden">
        {/* Background Image with Parallax Effect */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat transform scale-110"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1601584115197-04ecc0da31d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')`
          }}
        />
        <div className="absolute inset-0 hero-overlay" />
        
        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-warning bg-opacity-20 rounded-full animate-pulse delay-200"></div>
        <div className="absolute bottom-32 left-20 w-12 h-12 bg-success bg-opacity-20 rounded-full animate-pulse delay-400"></div>
        
        <div className="relative max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Hero Content */}
            <div className="text-white slide-in-left">
              <div className="mb-6">
                <span className="inline-block bg-warning text-dark-grey px-4 py-2 rounded-full text-sm font-semibold mb-4 fade-in delay-100">
                  🚛 Rejoignez-nous maintenant
                </span>
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight slide-up delay-200">
                {t('hero.mainTitle')}<br />
                <span className="text-warning">{t('hero.mainHighlight')}</span> {t('hero.mainSubtitle')}
              </h1>

              <p className="text-xl md:text-2xl mb-8 font-medium opacity-95 leading-relaxed slide-up delay-300">
                {t('hero.description')}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8 slide-up delay-400">
                <Button 
                  onClick={() => scrollToSection('apply')}
                  className="btn-warning text-dark-grey px-8 py-4 text-lg font-bold rounded-xl"
                >
                  <UserPlus className="mr-2" size={20} />
                  Postulez Maintenant
                </Button>
                <Button 
                  onClick={() => scrollToSection('how-it-works')}
                  variant="outline" 
                  className="border-2 border-white text-white px-8 py-4 text-lg font-semibold hover:bg-white hover:text-dark-grey transition-all duration-300 rounded-xl"
                >
                  <PlayCircle className="mr-2" size={20} />
                  Comment ça marche
                </Button>
              </div>
              
              {/* Trust indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm opacity-90 slide-up delay-500">
                <div className="flex items-center justify-center sm:justify-start">
                  <CheckCircle className="mr-2 text-success flex-shrink-0" size={16} />
                  <span className="whitespace-nowrap">500+ Chauffeurs</span>
                </div>
                <div className="flex items-center justify-center sm:justify-start">
                  <CheckCircle className="mr-2 text-success flex-shrink-0" size={16} />
                  <span className="whitespace-nowrap">Paiements garantis</span>
                </div>
                <div className="flex items-center justify-center sm:justify-start">
                  <CheckCircle className="mr-2 text-success flex-shrink-0" size={16} />
                  <span className="whitespace-nowrap">Support 24/7</span>
                </div>
              </div>
            </div>
            
            {/* Hero Image/Stats */}
            <div className="lg:block slide-in-right delay-300">
              <div className="relative">
                {/* Main stats card */}
                <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl">
                  <h3 className="text-2xl font-bold text-dark-grey mb-6">Rejoignez notre réseau</h3>
                  
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">500+</div>
                      <div className="text-sm text-gray-600">{t('hero.stats.drivers')}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-success mb-2">1000+</div>
                      <div className="text-sm text-gray-600">{t('hero.stats.missions')}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-warning mb-2">24h</div>
                      <div className="text-sm text-gray-600">{t('hero.stats.response')}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary mb-2">100%</div>
                      <div className="text-sm text-gray-600">{t('hero.stats.payments')}</div>
                    </div>
                  </div>
                  
                  <Button
                    onClick={() => scrollToSection('apply')}
                    className="w-full btn-primary text-white py-3 font-semibold rounded-xl"
                  >
                    {t('hero.joinNow')}
                  </Button>
                </div>
                
                {/* Floating elements around the card */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-warning rounded-full"></div>
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-success rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce cursor-pointer"
          onClick={() => scrollToSection('benefits')}
        >
          <div className="flex flex-col items-center">
            <span className="text-sm mb-2 opacity-75">{t('hero.discover')}</span>
            <ChevronDown size={32} />
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-gradient-to-br from-neutral to-light-grey">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16 slide-up">
            <span className="inline-block bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
              Nos Avantages
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-dark-grey mb-6">
              {t('benefits.sectionTitle')}
            </h2>
            <div className="section-divider w-32 mx-auto mb-6" />
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('benefits.sectionSubtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Paiement Rapide */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-100">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <div className="text-white text-4xl font-bold">💰</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('benefits.payment.title')}</h3>
                  <p className="text-gray-600 leading-relaxed">{t('benefits.payment.desc')}</p>
                </div>
              </CardContent>
            </Card>

            {/* Flexibilité */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-200">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-success/5 to-success/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-success to-green-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <div className="text-white text-4xl font-bold">📅</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('benefits.regular.title')}</h3>
                  <p className="text-gray-600 leading-relaxed">{t('benefits.regular.desc')}</p>
                </div>
              </CardContent>
            </Card>

            {/* Assistance 24/7 */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-300">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-warning/5 to-warning/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-warning to-orange-500 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <div className="text-white text-4xl font-bold">📞</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('benefits.support24.title')}</h3>
                  <p className="text-gray-600 leading-relaxed">{t('benefits.support24.desc')}</p>
                </div>
              </CardContent>
            </Card>

            {/* Missions Inter-ville */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-400">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <div className="text-white text-4xl font-bold">🚛</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('benefits.intercity.title')}</h3>
                  <p className="text-gray-600 leading-relaxed">{t('benefits.intercity.desc')}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16 slide-up delay-500">
            <Button 
              onClick={() => scrollToSection('apply')}
              className="btn-primary text-white px-10 py-4 text-lg font-semibold rounded-xl"
            >
              <UserPlus className="mr-2" size={20} />
              {t('benefits.joinTeam')}
            </Button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-dark-grey mb-6">
                {t('about.title')}
              </h2>
              <div className="section-divider w-24 mb-6" />
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                {t('about.desc1')}
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                {t('about.desc2')}
              </p>
              
              <div className="grid grid-cols-2 gap-6">
                <Card className="bg-neutral">
                  <CardContent className="text-center p-4">
                    <div className="text-2xl font-bold text-primary mb-2">500+</div>
                    <div className="text-sm text-gray-600">{t('about.drivers')}</div>
                  </CardContent>
                </Card>
                <Card className="bg-neutral">
                  <CardContent className="text-center p-4">
                    <div className="text-2xl font-bold text-primary mb-2">1000+</div>
                    <div className="text-sm text-gray-600">{t('about.missions')}</div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1601584115197-04ecc0da31d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
                alt="Chauffeur professionnel de camion - TransLogistics"
                className="rounded-xl shadow-lg w-full h-auto"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-success rounded-full flex items-center justify-center">
                    <CheckCircle className="text-white" size={24} />
                  </div>
                  <div>
                    <div className="font-semibold text-dark-grey">Certifié</div>
                    <div className="text-sm text-gray-500">Transport Professionnel</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-16 bg-neutral">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-dark-grey mb-4">
              {t('howItWorks.title')}
            </h2>
            <div className="section-divider w-24 mx-auto mb-6" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('howItWorks.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks.step1.title')}</h3>
              <p className="text-gray-600 mb-6">
                {t('howItWorks.step1.desc')}
              </p>
              {/* Arrow for desktop */}
              <div className="hidden md:block absolute top-10 -right-4 text-primary">
                <ArrowRight size={32} />
              </div>
            </div>
            
            {/* Step 2 */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-success rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks.step2.title')}</h3>
              <p className="text-gray-600 mb-6">
                {t('howItWorks.step2.desc')}
              </p>
              {/* Arrow for desktop */}
              <div className="hidden md:block absolute top-10 -right-4 text-success">
                <ArrowRight size={32} />
              </div>
            </div>
            
            {/* Step 3 */}
            <div className="text-center">
              <div className="w-20 h-20 bg-yellow-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks.step3.title')}</h3>
              <p className="text-gray-600 mb-6">
                {t('howItWorks.step3.desc')}
              </p>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <Button 
              onClick={() => scrollToSection('apply')}
              className="bg-primary text-white px-8 py-4 text-lg font-semibold hover:bg-blue-700"
            >
              <Rocket className="mr-2" size={20} />
              {t('howItWorks.startNow')}
            </Button>
          </div>
        </div>
      </section>

      {/* Application Form Section */}
      <section id="apply" className="py-20 bg-gradient-to-br from-white to-light-grey">
        <div className="max-w-5xl mx-auto px-4">
          <div className="text-center mb-16 slide-up">
            <span className="inline-block bg-warning text-dark-grey px-6 py-3 rounded-full text-sm font-semibold mb-6">
              📝 Candidature
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-dark-grey mb-6">
              {t('application.title')}
            </h2>
            <div className="section-divider w-32 mx-auto mb-6" />
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('application.subtitle')}
            </p>
          </div>
          
          <Card className="shadow-2xl border-0 overflow-hidden slide-up delay-200">
            <CardContent className="p-8">
              <Form {...form}>
                <form onSubmit={form.handleSubmit((data) => submitApplication.mutate(data))} className="space-y-6">
                  
                  {/* Personal Information */}
                  <div className="border-b border-gray-200 pb-6">
                    <h3 className="text-xl font-semibold text-dark-grey mb-4 flex items-center">
                      <User className="mr-2 text-primary" size={24} />
                      {t('form.personalInfo')}
                    </h3>
                    
                    <div className="grid md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="fullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.fullName')} *</FormLabel>
                            <FormControl>
                              <Input placeholder={t('form.fullName')} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.phone')} *</FormLabel>
                            <FormControl>
                              <Input placeholder="+212 6XX XXX XXX" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.city')} *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('form.city')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {cities.map((city) => (
                                  <SelectItem key={city} value={city}>
                                    {t(`cities.${city}`)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.email')}</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  {/* Truck Information */}
                  <div className="border-b border-gray-200 pb-6">
                    <h3 className="text-xl font-semibold text-dark-grey mb-4 flex items-center">
                      <Truck className="mr-2 text-primary" size={24} />
                      {t('form.truckInfo')}
                    </h3>
                    
                    <div className="grid md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="truckType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.truckType')} *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('form.truckType')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {truckTypes.map((type) => (
                                  <SelectItem key={type} value={type}>
                                    {t(`trucks.${type}`)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="truckYear"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.truckYear')} *</FormLabel>
                            <FormControl>
                              <Input type="number" min="1990" max="2024" placeholder="2020" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="md:col-span-2">
                        <FormField
                          control={form.control}
                          name="registration"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('form.registration')} *</FormLabel>
                              <FormControl>
                                <Input placeholder="123456-A-12" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Document Upload */}
                  <div className="pb-6">
                    <h3 className="text-xl font-semibold text-dark-grey mb-6 flex items-center">
                      <Upload className="mr-2 text-primary" size={24} />
                      {t('form.documents')}
                    </h3>
                    
                    <div className="grid md:grid-cols-3 gap-6">
                      {/* ID Card */}
                      <div className="relative group">
                        <Label className="block text-sm font-semibold text-dark-grey mb-3">
                          {t('form.idCard')} *
                        </Label>
                        <div className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 cursor-pointer ${
                          files.idCard 
                            ? 'border-success bg-success/5 hover:bg-success/10' 
                            : 'border-gray-300 hover:border-primary hover:bg-primary/5'
                        }`}>
                          {files.idCard ? (
                            <div className="space-y-2">
                              <CheckCircle className="mx-auto text-success" size={32} />
                              <p className="text-sm font-medium text-success">Fichier téléchargé</p>
                              <p className="text-xs text-gray-600 truncate">{files.idCard.name}</p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto text-gray-400 group-hover:text-primary transition-colors" size={32} />
                              <p className="text-sm text-gray-600">Glisser-déposer ou cliquer</p>
                              <p className="text-xs text-gray-500">PDF, JPG, PNG (max. 5MB)</p>
                            </div>
                          )}
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            onChange={(e) => handleFileChange('idCard', e.target.files?.[0] || null)}
                          />
                        </div>
                      </div>
                      
                      {/* License */}
                      <div className="relative group">
                        <Label className="block text-sm font-semibold text-dark-grey mb-3">
                          {t('form.license')} *
                        </Label>
                        <div className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 cursor-pointer ${
                          files.license 
                            ? 'border-success bg-success/5 hover:bg-success/10' 
                            : 'border-gray-300 hover:border-primary hover:bg-primary/5'
                        }`}>
                          {files.license ? (
                            <div className="space-y-2">
                              <CheckCircle className="mx-auto text-success" size={32} />
                              <p className="text-sm font-medium text-success">Fichier téléchargé</p>
                              <p className="text-xs text-gray-600 truncate">{files.license.name}</p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto text-gray-400 group-hover:text-primary transition-colors" size={32} />
                              <p className="text-sm text-gray-600">Glisser-déposer ou cliquer</p>
                              <p className="text-xs text-gray-500">PDF, JPG, PNG (max. 5MB)</p>
                            </div>
                          )}
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            onChange={(e) => handleFileChange('license', e.target.files?.[0] || null)}
                          />
                        </div>
                      </div>
                      
                      {/* Truck Photo */}
                      <div className="relative group">
                        <Label className="block text-sm font-semibold text-dark-grey mb-3">
                          {t('form.truckPhoto')} *
                        </Label>
                        <div className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 cursor-pointer ${
                          files.truckPhoto 
                            ? 'border-success bg-success/5 hover:bg-success/10' 
                            : 'border-gray-300 hover:border-primary hover:bg-primary/5'
                        }`}>
                          {files.truckPhoto ? (
                            <div className="space-y-2">
                              <CheckCircle className="mx-auto text-success" size={32} />
                              <p className="text-sm font-medium text-success">Fichier téléchargé</p>
                              <p className="text-xs text-gray-600 truncate">{files.truckPhoto.name}</p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto text-gray-400 group-hover:text-primary transition-colors" size={32} />
                              <p className="text-sm text-gray-600">Glisser-déposer ou cliquer</p>
                              <p className="text-xs text-gray-500">JPG, PNG (max. 5MB)</p>
                            </div>
                          )}
                          <input
                            type="file"
                            accept="image/*"
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            onChange={(e) => handleFileChange('truckPhoto', e.target.files?.[0] || null)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Terms and Submit */}
                  <div className="pt-6">
                    <div className="flex items-start space-x-3 mb-6">
                      <Checkbox id="terms" required />
                      <Label htmlFor="terms" className="text-sm text-gray-600 leading-relaxed">
                        {t('form.terms')}
                      </Label>
                    </div>
                    
                    <Button
                      type="submit"
                      disabled={submitApplication.isPending}
                      className="w-full btn-primary text-white py-4 text-lg font-semibold rounded-xl"
                    >
                      {submitApplication.isPending ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          {t('application.sending')}
                        </div>
                      ) : (
                        <>
                          <Send className="mr-2" size={20} />
                          {t('application.send')}
                        </>
                      )}
                    </Button>
                    
                    <p className="text-sm text-gray-500 text-center mt-4 flex items-center justify-center">
                      <Shield className="mr-1" size={16} />
                      {t('form.privacy')}
                    </p>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-primary text-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16 slide-up">
            <span className="inline-block bg-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
              💬 Témoignages
            </span>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              {t('testimonials.title')}
            </h2>
            <div className="w-32 h-1 bg-warning mx-auto rounded-full" />
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 slide-up delay-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-warning rounded-full flex items-center justify-center mr-4">
                  <span className="text-dark-grey font-bold">MH</span>
                </div>
                <div>
                  <h4 className="font-semibold">Mohamed Hassan</h4>
                  <p className="text-sm opacity-75">Chauffeur depuis 2 ans</p>
                </div>
              </div>
              <p className="text-white/90 leading-relaxed">
                {t('testimonials.mohamed.text')}
              </p>
            </div>
            
            {/* Testimonial 2 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 slide-up delay-200">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-warning rounded-full flex items-center justify-center mr-4">
                  <span className="text-dark-grey font-bold">AK</span>
                </div>
                <div>
                  <h4 className="font-semibold">Ahmed Khalil</h4>
                  <p className="text-sm opacity-75">Chauffeur depuis 3 ans</p>
                </div>
              </div>
              <p className="text-white/90 leading-relaxed">
                {t('testimonials.ahmed.text')}
              </p>
            </div>
            
            {/* Testimonial 3 */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 slide-up delay-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-warning rounded-full flex items-center justify-center mr-4">
                  <span className="text-dark-grey font-bold">YB</span>
                </div>
                <div>
                  <h4 className="font-semibold">Youssef Benali</h4>
                  <p className="text-sm opacity-75">Chauffeur depuis 1 an</p>
                </div>
              </div>
              <p className="text-white/90 leading-relaxed">
                {t('testimonials.youssef.text')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gradient-to-br from-neutral to-light-grey">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16 slide-up">
            <span className="inline-block bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
              📞 Contact
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-dark-grey mb-6">
              {t('contact.sectionTitle')}
            </h2>
            <div className="section-divider w-32 mx-auto mb-6" />
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('contact.sectionSubtitle')}
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {/* WhatsApp */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-100">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <div className="text-white text-4xl font-bold">💬</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('contact.whatsapp.title')}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{t('contact.whatsapp.desc')}</p>
                  <a
                    href={`https://wa.me/${whatsappNumber.replace('+', '')}?text=Bonjour, je suis chauffeur avec un camion, je veux rejoindre votre réseau.`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button className="bg-green-500 text-white px-6 py-3 font-semibold hover:bg-green-600 rounded-xl transition-all duration-300">
                      <div className="mr-2">💬</div>
                      {t('contact.whatsapp.action')}
                    </Button>
                  </a>
                </div>
              </CardContent>
            </Card>
            
            {/* Phone */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-200">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <div className="text-white text-4xl font-bold">📞</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('contact.phone.title')}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{t('contact.phone.desc')}</p>
                  <a href={`tel:${phoneNumber}`}>
                    <Button className="btn-primary text-white px-6 py-3 font-semibold rounded-xl">
                      <div className="mr-2">📞</div>
                      {phoneNumber}
                    </Button>
                  </a>
                </div>
              </CardContent>
            </Card>
            
            {/* Email */}
            <Card className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white border-0 slide-up delay-300">
              <CardContent className="p-8 text-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-warning/5 to-warning/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-warning to-orange-500 rounded-2xl flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <div className="text-white text-4xl font-bold">✉️</div>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-dark-grey">{t('contact.email.title')}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{t('contact.email.desc')}</p>
                  <a href={`mailto:${email}`}>
                    <Button className="btn-warning text-dark-grey px-6 py-3 font-semibold rounded-xl">
                      <div className="mr-2">✉️</div>
                      {t('contact.email.action')}
                    </Button>
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* CTA Section */}
          <div className="text-center mt-16 slide-up delay-400">
            <div className="bg-primary rounded-2xl p-8 md:p-12 text-white">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                {t('contact.cta.title')}
              </h3>
              <p className="text-lg mb-8 opacity-90">
                {t('contact.cta.subtitle')}
              </p>
              <Button
                onClick={() => scrollToSection('apply')}
                className="btn-warning text-dark-grey px-8 py-4 text-lg font-semibold rounded-xl"
              >
                <UserPlus className="mr-2" size={20} />
                {t('contact.cta.apply')}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}