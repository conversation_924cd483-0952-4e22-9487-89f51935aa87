import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { MessageCircle, Phone, Mail } from "lucide-react";

export default function Contact() {
  const { t } = useTranslation();

  const phoneNumber = "+212500000000"; // Replace with actual phone number
  const whatsappNumber = "+212600000000"; // Replace with actual WhatsApp number
  const email = "<EMAIL>"; // Replace with actual email

  return (
    <div className="min-h-screen pt-16 bg-neutral">
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-dark-grey mb-4">
              {t('contact.title')}
            </h1>
            <div className="section-divider w-24 mx-auto mb-6" />
            <p className="text-lg text-gray-600">
              {t('contact.subtitle')}
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {/* WhatsApp */}
            <Card className="hover:shadow-xl transition-shadow">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-success rounded-full flex items-center justify-center mb-6 mx-auto">
                  <MessageCircle className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">{t('contact.whatsapp.title')}</h3>
                <p className="text-gray-600 mb-6">{t('contact.whatsapp.desc')}</p>
                <a 
                  href={`https://wa.me/${whatsappNumber.replace('+', '')}?text=Bonjour, je suis intéressé par vos services de transport`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button className="bg-success text-white px-6 py-3 font-semibold hover:bg-green-600">
                    <MessageCircle className="mr-2" size={20} />
                    {t('contact.whatsapp.button')}
                  </Button>
                </a>
              </CardContent>
            </Card>
            
            {/* Phone */}
            <Card className="hover:shadow-xl transition-shadow">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto">
                  <Phone className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">{t('contact.phone.title')}</h3>
                <p className="text-gray-600 mb-6">{t('contact.phone.desc')}</p>
                <a href={`tel:${phoneNumber}`}>
                  <Button className="bg-primary text-white px-6 py-3 font-semibold hover:bg-blue-700">
                    <Phone className="mr-2" size={20} />
                    {phoneNumber}
                  </Button>
                </a>
              </CardContent>
            </Card>
            
            {/* Email */}
            <Card className="hover:shadow-xl transition-shadow">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <Mail className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-bold mb-4">{t('contact.email.title')}</h3>
                <p className="text-gray-600 mb-6">{t('contact.email.desc')}</p>
                <a href={`mailto:${email}`}>
                  <Button className="bg-yellow-500 text-white px-6 py-3 font-semibold hover:bg-yellow-600">
                    <Mail className="mr-2" size={20} />
                    {t('contact.email.button')}
                  </Button>
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
