import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { CheckCircle } from "lucide-react";

export default function About() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-dark-grey mb-6">
                {t('about.title')}
              </h1>
              <div className="section-divider w-24 mb-6" />
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                {t('about.desc1')}
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                {t('about.desc2')}
              </p>
              
              <div className="grid grid-cols-2 gap-6">
                <Card className="bg-neutral">
                  <CardContent className="text-center p-4">
                    <div className="text-2xl font-bold text-primary mb-2">500+</div>
                    <div className="text-sm text-gray-600">{t('about.drivers')}</div>
                  </CardContent>
                </Card>
                <Card className="bg-neutral">
                  <CardContent className="text-center p-4">
                    <div className="text-2xl font-bold text-primary mb-2">1000+</div>
                    <div className="text-sm text-gray-600">{t('about.missions')}</div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                alt="Professional truck driver" 
                className="rounded-xl shadow-lg w-full h-auto"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-success rounded-full flex items-center justify-center">
                    <CheckCircle className="text-white" size={24} />
                  </div>
                  <div>
                    <div className="font-semibold text-dark-grey">Certifié</div>
                    <div className="text-sm text-gray-500">Transport Professionnel</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
