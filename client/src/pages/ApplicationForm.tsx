import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { insertApplicationSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { User, Truck, Upload, Send, Shield } from "lucide-react";

export default function ApplicationForm() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [files, setFiles] = useState<{
    idCard?: File;
    license?: File;
    truckPhoto?: File;
  }>({});

  const form = useForm({
    resolver: zodResolver(insertApplicationSchema),
    defaultValues: {
      fullName: "",
      phone: "",
      city: "",
      email: "",
      truckType: "",
      truckYear: "",
      registration: "",
    },
  });

  const submitApplication = useMutation({
    mutationFn: async (data: any) => {
      const formData = new FormData();
      
      // Add form fields
      Object.keys(data).forEach(key => {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      });
      
      // Add files
      if (files.idCard) formData.append('idCard', files.idCard);
      if (files.license) formData.append('license', files.license);
      if (files.truckPhoto) formData.append('truckPhoto', files.truckPhoto);
      
      const response = await fetch('/api/applications', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erreur lors de la soumission');
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Candidature envoyée!",
        description: "Merci pour votre candidature. Nous vous contacterons bientôt.",
      });
      form.reset();
      setFiles({});
    },
    onError: (error: any) => {
      toast({
        title: "Erreur",
        description: error.message || "Erreur lors de la soumission de la candidature",
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (field: 'idCard' | 'license' | 'truckPhoto', file: File | null) => {
    if (file) {
      setFiles(prev => ({ ...prev, [field]: file }));
    }
  };

  const cities = [
    'casablanca', 'rabat', 'fes', 'marrakech', 'agadir', 'tanger', 
    'meknes', 'oujda', 'kenitra', 'tetouan', 'other'
  ];

  const truckTypes = [
    'porteur', 'semiRemorque', 'fourgon', 'frigorifique', 'citerne', 'benne'
  ];

  return (
    <div className="min-h-screen pt-16 bg-neutral">
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-dark-grey mb-4">
              {t('form.title')}
            </h1>
            <div className="section-divider w-24 mx-auto mb-6" />
            <p className="text-lg text-gray-600">
              {t('form.subtitle')}
            </p>
          </div>
          
          <Card className="shadow-lg">
            <CardContent className="p-8">
              <Form {...form}>
                <form onSubmit={form.handleSubmit((data) => submitApplication.mutate(data))} className="space-y-6">
                  
                  {/* Personal Information */}
                  <div className="border-b border-gray-200 pb-6">
                    <h3 className="text-xl font-semibold text-dark-grey mb-4 flex items-center">
                      <User className="mr-2 text-primary" size={24} />
                      {t('form.personalInfo')}
                    </h3>
                    
                    <div className="grid md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="fullName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.fullName')} *</FormLabel>
                            <FormControl>
                              <Input placeholder={t('form.fullName')} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.phone')} *</FormLabel>
                            <FormControl>
                              <Input placeholder="+212 6XX XXX XXX" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.city')} *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('form.city')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {cities.map((city) => (
                                  <SelectItem key={city} value={city}>
                                    {t(`cities.${city}`)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.email')}</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  {/* Truck Information */}
                  <div className="border-b border-gray-200 pb-6">
                    <h3 className="text-xl font-semibold text-dark-grey mb-4 flex items-center">
                      <Truck className="mr-2 text-primary" size={24} />
                      {t('form.truckInfo')}
                    </h3>
                    
                    <div className="grid md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="truckType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.truckType')} *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('form.truckType')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {truckTypes.map((type) => (
                                  <SelectItem key={type} value={type}>
                                    {t(`trucks.${type}`)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="truckYear"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.truckYear')} *</FormLabel>
                            <FormControl>
                              <Input type="number" min="1990" max="2024" placeholder="2020" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="md:col-span-2">
                        <FormField
                          control={form.control}
                          name="registration"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('form.registration')} *</FormLabel>
                              <FormControl>
                                <Input placeholder="123456-A-12" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                  
                  {/* Document Upload */}
                  <div className="pb-6">
                    <h3 className="text-xl font-semibold text-dark-grey mb-4 flex items-center">
                      <Upload className="mr-2 text-primary" size={24} />
                      {t('form.documents')}
                    </h3>
                    
                    <div className="grid md:grid-cols-3 gap-6">
                      {/* ID Card */}
                      <div>
                        <Label className="block text-sm font-semibold text-dark-grey mb-2">
                          {t('form.idCard')} *
                        </Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary transition-colors cursor-pointer">
                          <Upload className="mx-auto text-gray-400 mb-2" size={32} />
                          <p className="text-sm text-gray-500">
                            {files.idCard ? files.idCard.name : t('form.upload')}
                          </p>
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            className="hidden"
                            onChange={(e) => handleFileChange('idCard', e.target.files?.[0] || null)}
                            id="idCard"
                          />
                          <label htmlFor="idCard" className="cursor-pointer absolute inset-0" />
                        </div>
                      </div>
                      
                      {/* License */}
                      <div>
                        <Label className="block text-sm font-semibold text-dark-grey mb-2">
                          {t('form.license')} *
                        </Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary transition-colors cursor-pointer">
                          <Upload className="mx-auto text-gray-400 mb-2" size={32} />
                          <p className="text-sm text-gray-500">
                            {files.license ? files.license.name : t('form.upload')}
                          </p>
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            className="hidden"
                            onChange={(e) => handleFileChange('license', e.target.files?.[0] || null)}
                            id="license"
                          />
                          <label htmlFor="license" className="cursor-pointer absolute inset-0" />
                        </div>
                      </div>
                      
                      {/* Truck Photo */}
                      <div>
                        <Label className="block text-sm font-semibold text-dark-grey mb-2">
                          {t('form.truckPhoto')} *
                        </Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary transition-colors cursor-pointer">
                          <Upload className="mx-auto text-gray-400 mb-2" size={32} />
                          <p className="text-sm text-gray-500">
                            {files.truckPhoto ? files.truckPhoto.name : t('form.upload')}
                          </p>
                          <input
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={(e) => handleFileChange('truckPhoto', e.target.files?.[0] || null)}
                            id="truckPhoto"
                          />
                          <label htmlFor="truckPhoto" className="cursor-pointer absolute inset-0" />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Terms and Submit */}
                  <div className="pt-6">
                    <div className="flex items-start space-x-3 mb-6">
                      <Checkbox id="terms" required />
                      <Label htmlFor="terms" className="text-sm text-gray-600 leading-relaxed">
                        {t('form.terms')}
                      </Label>
                    </div>
                    
                    <Button
                      type="submit"
                      disabled={submitApplication.isPending}
                      className="w-full bg-primary text-white py-4 text-lg font-semibold hover:bg-blue-700"
                    >
                      {submitApplication.isPending ? (
                        "Envoi en cours..."
                      ) : (
                        <>
                          <Send className="mr-2" size={20} />
                          {t('form.submit')}
                        </>
                      )}
                    </Button>
                    
                    <p className="text-sm text-gray-500 text-center mt-4 flex items-center justify-center">
                      <Shield className="mr-1" size={16} />
                      {t('form.privacy')}
                    </p>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
