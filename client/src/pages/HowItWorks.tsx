import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { useTranslation } from "react-i18next";
import { ArrowR<PERSON>, <PERSON> } from "lucide-react";

export default function HowItWorks() {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen pt-16">
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-dark-grey mb-4">
              {t('howItWorks.title')}
            </h1>
            <div className="section-divider w-24 mx-auto mb-6" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('howItWorks.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks.step1.title')}</h3>
              <p className="text-gray-600 mb-6">
                {t('howItWorks.step1.desc')}
              </p>
              {/* Arrow for desktop */}
              <div className="hidden md:block absolute top-10 -right-4 text-primary">
                <ArrowRight size={32} />
              </div>
            </div>
            
            {/* Step 2 */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-success rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks.step2.title')}</h3>
              <p className="text-gray-600 mb-6">
                {t('howItWorks.step2.desc')}
              </p>
              {/* Arrow for desktop */}
              <div className="hidden md:block absolute top-10 -right-4 text-success">
                <ArrowRight size={32} />
              </div>
            </div>
            
            {/* Step 3 */}
            <div className="text-center">
              <div className="w-20 h-20 bg-yellow-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks.step3.title')}</h3>
              <p className="text-gray-600 mb-6">
                {t('howItWorks.step3.desc')}
              </p>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <Link href="/apply">
              <Button className="bg-primary text-white px-8 py-4 text-lg font-semibold hover:bg-blue-700">
                <Rocket className="mr-2" size={20} />
                {t('howItWorks.startNow')}
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
